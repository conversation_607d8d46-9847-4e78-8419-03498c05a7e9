cmake_minimum_required(VERSION 3.14)
project(gRPC_server_demo)

# set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)  # 可选：禁用编译器扩展

# 导出编译命令给clangd使用
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 替换单个源文件为 client 和 server 目录下的所有 cpp 文件，并将结果存储在变量中
file(GLOB CLIENT_SRC "src/client/*.cpp")
file(GLOB SERVER_SRC "src/server/*.cpp")
set(ALL_SRC_FILES ${CLIENT_SRC} ${SERVER_SRC})  # maybe useless
file(GLOB PROTO_FILES "proto/*.proto")

find_package(Protobuf REQUIRED)
find_package(absl REQUIRED)
message(STATUS "Found absl: ${absl_FOUND}")
message(STATUS "absl version: ${absl_VERSION}")

include_directories(${Protobuf_INCLUDE_DIRS})
include_directories(${CMAKE_CURRENT_BINARY_DIR})

# 生成 C++ 文件
protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS ${PROTO_FILES})

# 创建可执行文件
add_executable(gRPC_server_client ${CLIENT_SRC} ${PROTO_SRCS} ${PROTO_HDRS})

# 链接 protobuf 库
target_link_libraries(gRPC_server ${Protobuf_LIBRARIES} 
    absl::log
    absl::strings
    absl::base
    absl::failure_signal_handler
    absl::check
)

  
