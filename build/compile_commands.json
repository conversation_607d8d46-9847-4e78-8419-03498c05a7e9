[{"directory": "/Users/<USER>/code/gRPC/build", "command": "/usr/bin/c++  -I/opt/homebrew/include -I/Users/<USER>/code/gRPC/build -std=c++20 -arch arm64 -o CMakeFiles/gRPC_client.dir/greeter.pb.cc.o -c /Users/<USER>/code/gRPC/build/greeter.pb.cc", "file": "/Users/<USER>/code/gRPC/build/greeter.pb.cc", "output": "CMakeFiles/gRPC_client.dir/greeter.pb.cc.o"}, {"directory": "/Users/<USER>/code/gRPC/build", "command": "/usr/bin/c++  -I/Users/<USER>/code/gRPC/build -isystem /opt/homebrew/include -std=c++20 -arch arm64 -o CMakeFiles/gRPC_server.dir/greeter.pb.cc.o -c /Users/<USER>/code/gRPC/build/greeter.pb.cc", "file": "/Users/<USER>/code/gRPC/build/greeter.pb.cc", "output": "CMakeFiles/gRPC_server.dir/greeter.pb.cc.o"}]