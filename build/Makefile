# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/gRPC

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/gRPC/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles /Users/<USER>/code/gRPC/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named gRPC_client

# Build rule for target.
gRPC_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gRPC_client
.PHONY : gRPC_client

# fast build rule for target.
gRPC_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/build
.PHONY : gRPC_client/fast

#=============================================================================
# Target rules for targets named gRPC_server

# Build rule for target.
gRPC_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gRPC_server
.PHONY : gRPC_server

# fast build rule for target.
gRPC_server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/build
.PHONY : gRPC_server/fast

greeter.pb.o: greeter.pb.cc.o
.PHONY : greeter.pb.o

# target to build an object file
greeter.pb.cc.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/greeter.pb.cc.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/greeter.pb.cc.o
.PHONY : greeter.pb.cc.o

greeter.pb.i: greeter.pb.cc.i
.PHONY : greeter.pb.i

# target to preprocess a source file
greeter.pb.cc.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/greeter.pb.cc.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/greeter.pb.cc.i
.PHONY : greeter.pb.cc.i

greeter.pb.s: greeter.pb.cc.s
.PHONY : greeter.pb.s

# target to generate assembly for a file
greeter.pb.cc.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/greeter.pb.cc.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/greeter.pb.cc.s
.PHONY : greeter.pb.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... gRPC_client"
	@echo "... gRPC_server"
	@echo "... greeter.pb.o"
	@echo "... greeter.pb.i"
	@echo "... greeter.pb.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

