# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/gRPC

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/gRPC/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/gRPC_client.dir/all
all: CMakeFiles/gRPC_server.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/gRPC_client.dir/codegen
codegen: CMakeFiles/gRPC_server.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/gRPC_client.dir/clean
clean: CMakeFiles/gRPC_server.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/gRPC_client.dir

# All Build rule for target.
CMakeFiles/gRPC_client.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=1,2,3 "Built target gRPC_client"
.PHONY : CMakeFiles/gRPC_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/gRPC_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/gRPC_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles 0
.PHONY : CMakeFiles/gRPC_client.dir/rule

# Convenience name for target.
gRPC_client: CMakeFiles/gRPC_client.dir/rule
.PHONY : gRPC_client

# codegen rule for target.
CMakeFiles/gRPC_client.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=1,2,3 "Finished codegen for target gRPC_client"
.PHONY : CMakeFiles/gRPC_client.dir/codegen

# clean rule for target.
CMakeFiles/gRPC_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_client.dir/build.make CMakeFiles/gRPC_client.dir/clean
.PHONY : CMakeFiles/gRPC_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/gRPC_server.dir

# All Build rule for target.
CMakeFiles/gRPC_server.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=4,5,6 "Built target gRPC_server"
.PHONY : CMakeFiles/gRPC_server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/gRPC_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/gRPC_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/code/gRPC/build/CMakeFiles 0
.PHONY : CMakeFiles/gRPC_server.dir/rule

# Convenience name for target.
gRPC_server: CMakeFiles/gRPC_server.dir/rule
.PHONY : gRPC_server

# codegen rule for target.
CMakeFiles/gRPC_server.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=4,5,6 "Finished codegen for target gRPC_server"
.PHONY : CMakeFiles/gRPC_server.dir/codegen

# clean rule for target.
CMakeFiles/gRPC_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gRPC_server.dir/build.make CMakeFiles/gRPC_server.dir/clean
.PHONY : CMakeFiles/gRPC_server.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

