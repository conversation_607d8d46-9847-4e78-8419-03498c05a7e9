# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/code/gRPC

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/code/gRPC/build

# Include any dependencies generated for this target.
include CMakeFiles/gRPC_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/gRPC_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/gRPC_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/gRPC_client.dir/flags.make

greeter.pb.h: /Users/<USER>/code/gRPC/proto/greeter.proto
greeter.pb.h: /opt/homebrew/bin/protoc
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running cpp protocol buffer compiler on /Users/<USER>/code/gRPC/proto/greeter.proto"
	/opt/homebrew/bin/protoc --cpp_out :/Users/<USER>/code/gRPC/build -I /Users/<USER>/code/gRPC/proto /Users/<USER>/code/gRPC/proto/greeter.proto

greeter.pb.cc: greeter.pb.h
	@$(CMAKE_COMMAND) -E touch_nocreate greeter.pb.cc

CMakeFiles/gRPC_client.dir/codegen:
.PHONY : CMakeFiles/gRPC_client.dir/codegen

CMakeFiles/gRPC_client.dir/greeter.pb.cc.o: CMakeFiles/gRPC_client.dir/flags.make
CMakeFiles/gRPC_client.dir/greeter.pb.cc.o: greeter.pb.cc
CMakeFiles/gRPC_client.dir/greeter.pb.cc.o: CMakeFiles/gRPC_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/gRPC_client.dir/greeter.pb.cc.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/gRPC_client.dir/greeter.pb.cc.o -MF CMakeFiles/gRPC_client.dir/greeter.pb.cc.o.d -o CMakeFiles/gRPC_client.dir/greeter.pb.cc.o -c /Users/<USER>/code/gRPC/build/greeter.pb.cc

CMakeFiles/gRPC_client.dir/greeter.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/gRPC_client.dir/greeter.pb.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/code/gRPC/build/greeter.pb.cc > CMakeFiles/gRPC_client.dir/greeter.pb.cc.i

CMakeFiles/gRPC_client.dir/greeter.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/gRPC_client.dir/greeter.pb.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/code/gRPC/build/greeter.pb.cc -o CMakeFiles/gRPC_client.dir/greeter.pb.cc.s

# Object files for target gRPC_client
gRPC_client_OBJECTS = \
"CMakeFiles/gRPC_client.dir/greeter.pb.cc.o"

# External object files for target gRPC_client
gRPC_client_EXTERNAL_OBJECTS =

gRPC_client: CMakeFiles/gRPC_client.dir/greeter.pb.cc.o
gRPC_client: CMakeFiles/gRPC_client.dir/build.make
gRPC_client: CMakeFiles/gRPC_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/code/gRPC/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable gRPC_client"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/gRPC_client.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/gRPC_client.dir/build: gRPC_client
.PHONY : CMakeFiles/gRPC_client.dir/build

CMakeFiles/gRPC_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/gRPC_client.dir/cmake_clean.cmake
.PHONY : CMakeFiles/gRPC_client.dir/clean

CMakeFiles/gRPC_client.dir/depend: greeter.pb.cc
CMakeFiles/gRPC_client.dir/depend: greeter.pb.h
	cd /Users/<USER>/code/gRPC/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/code/gRPC /Users/<USER>/code/gRPC /Users/<USER>/code/gRPC/build /Users/<USER>/code/gRPC/build /Users/<USER>/code/gRPC/build/CMakeFiles/gRPC_client.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/gRPC_client.dir/depend

