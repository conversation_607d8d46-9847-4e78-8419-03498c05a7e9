/usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/gRPC_server.dir/greeter.pb.cc.o -o gRPC_server  -Wl,-rpath,/opt/homebrew/lib /opt/homebrew/lib/libprotobuf.dylib /opt/homebrew/lib/libabsl_failure_signal_handler.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_check_op.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_conditions.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_message.2407.0.0.dylib /opt/homebrew/lib/libabsl_examine_stack.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_nullguard.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_format.2407.0.0.dylib /opt/homebrew/lib/libabsl_str_format_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_proto.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_log_sink_set.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_globals.2407.0.0.dylib /opt/homebrew/lib/libabsl_vlog_config_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_internal_fnmatch.2407.0.0.dylib /opt/homebrew/lib/libabsl_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_optional_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_city.2407.0.0.dylib /opt/homebrew/lib/libabsl_bad_variant_access.2407.0.0.dylib /opt/homebrew/lib/libabsl_low_level_hash.2407.0.0.dylib /opt/homebrew/lib/libabsl_synchronization.2407.0.0.dylib /opt/homebrew/lib/libabsl_stacktrace.2407.0.0.dylib /opt/homebrew/lib/libabsl_symbolize.2407.0.0.dylib /opt/homebrew/lib/libabsl_debugging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_demangle_rust.2407.0.0.dylib /opt/homebrew/lib/libabsl_decode_rust_punycode.2407.0.0.dylib /opt/homebrew/lib/libabsl_utf8_for_code_point.2407.0.0.dylib /opt/homebrew/lib/libabsl_graphcycles_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_malloc_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_kernel_timeout_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_sink.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_entry.2407.0.0.dylib /opt/homebrew/lib/libabsl_strerror.2407.0.0.dylib /opt/homebrew/lib/libabsl_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_civil_time.2407.0.0.dylib /opt/homebrew/lib/libabsl_time_zone.2407.0.0.dylib -Wl,-framework,CoreFoundation /opt/homebrew/lib/libabsl_strings.2407.0.0.dylib /opt/homebrew/lib/libabsl_strings_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_string_view.2407.0.0.dylib /opt/homebrew/lib/libabsl_base.2407.0.0.dylib /opt/homebrew/lib/libabsl_spinlock_wait.2407.0.0.dylib /opt/homebrew/lib/libabsl_int128.2407.0.0.dylib /opt/homebrew/lib/libabsl_throw_delegate.2407.0.0.dylib /opt/homebrew/lib/libabsl_raw_logging_internal.2407.0.0.dylib /opt/homebrew/lib/libabsl_log_severity.2407.0.0.dylib
