# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/code/gRPC/CMakeLists.txt"
  "CMakeFiles/4.0.2/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.2/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.0.2/CMakeSystem.cmake"
  "/opt/homebrew/lib/cmake/absl/abslConfig.cmake"
  "/opt/homebrew/lib/cmake/absl/abslConfigVersion.cmake"
  "/opt/homebrew/lib/cmake/absl/abslTargets-release.cmake"
  "/opt/homebrew/lib/cmake/absl/abslTargets.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/homebrew/share/cmake/Modules/CheckCSourceCompiles.cmake"
  "/opt/homebrew/share/cmake/Modules/CheckIncludeFile.cmake"
  "/opt/homebrew/share/cmake/Modules/CheckLibraryExists.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"
  "/opt/homebrew/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "/opt/homebrew/share/cmake/Modules/FindPackageMessage.cmake"
  "/opt/homebrew/share/cmake/Modules/FindProtobuf.cmake"
  "/opt/homebrew/share/cmake/Modules/FindThreads.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/opt/homebrew/share/cmake/Modules/Internal/CheckSourceCompiles.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Linker/AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Apple-Clang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"
  "/opt/homebrew/share/cmake/Modules/SelectLibraryConfigurations.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/gRPC_client.dir/DependInfo.cmake"
  "CMakeFiles/gRPC_server.dir/DependInfo.cmake"
  )
